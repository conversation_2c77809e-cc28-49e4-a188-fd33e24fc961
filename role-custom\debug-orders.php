<?php
/**
 * Debug dosyası - Sipariş filtreleme sorunlarını test etmek için
 */

// Doğrudan erişimi engelle
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Superole kullanıcısının sipariş verilerini debug et
 */
function debug_superole_orders() {
    // Sadece admin panelinde çalışsın
    if (!is_admin()) {
        return;
    }

    // Mevcut kullanıcıyı al
    $current_user = wp_get_current_user();
    
    // Superole rolünde mi kontrol et
    if (!in_array('superole', $current_user->roles)) {
        return;
    }

    $current_user_id = $current_user->ID;
    
    echo '<div class="notice notice-info" style="padding: 15px;">';
    echo '<h3>🔍 Superole Sipariş Debug Bilgileri</h3>';
    echo '<p><em>Bu bilgiler sadece superole rolündeki kullanıcılar için görüntülenir.</em></p>';
    
    // Kullanıcının ürünlerini al
    $user_products = get_posts([
        'post_type' => 'product',
        'author' => $current_user_id,
        'posts_per_page' => -1,
        'fields' => 'ids',
        'post_status' => 'any'
    ]);
    
    // Mevcut sayfa bilgisi
    global $pagenow;
    $current_page = $pagenow;
    if (isset($_GET['page'])) {
        $current_page .= '?page=' . $_GET['page'];
    }
    if (isset($_GET['post_type'])) {
        $current_page .= '&post_type=' . $_GET['post_type'];
    }

    echo '<p><strong>Kullanıcı ID:</strong> ' . $current_user_id . '</p>';
    echo '<p><strong>Mevcut Sayfa:</strong> ' . $current_page . '</p>';
    echo '<p><strong>Kullanıcının Ürün Sayısı:</strong> ' . count($user_products) . '</p>';
    
    if (!empty($user_products)) {
        echo '<p><strong>Ürün ID\'leri:</strong> ' . implode(', ', $user_products) . '</p>';
        
        // Bu ürünleri içeren siparişleri bul
        global $wpdb;
        $product_ids_str = implode(',', array_map('intval', $user_products));
        
        $order_ids = $wpdb->get_col("
            SELECT DISTINCT order_items.order_id
            FROM {$wpdb->prefix}woocommerce_order_items as order_items
            INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta as order_item_meta
                ON order_items.order_item_id = order_item_meta.order_item_id
            WHERE order_item_meta.meta_key = '_product_id'
                AND order_item_meta.meta_value IN ({$product_ids_str})
        ");
        
        echo '<p><strong>Bu Ürünleri İçeren Sipariş Sayısı:</strong> ' . count($order_ids) . '</p>';
        
        if (!empty($order_ids)) {
            echo '<p><strong>Sipariş ID\'leri:</strong> ' . implode(', ', $order_ids) . '</p>';
            
            // Her sipariş için detay bilgi
            foreach ($order_ids as $order_id) {
                $order = wc_get_order($order_id);
                if ($order) {
                    echo '<p><strong>Sipariş #' . $order_id . ':</strong> ';
                    echo 'Durum: ' . $order->get_status() . ', ';
                    echo 'Tarih: ' . $order->get_date_created()->format('Y-m-d H:i:s') . ', ';
                    echo 'Toplam: ' . $order->get_total() . '</p>';
                }
            }
        }
    } else {
        echo '<p><strong>Uyarı:</strong> Bu kullanıcının hiç ürünü yok!</p>';
    }
    
    // Tüm siparişleri kontrol et - HPOS için
    $all_orders = wc_get_orders([
        'limit' => -1,
        'status' => 'any'
    ]);

    echo '<p><strong>WC_Get_Orders ile Toplam Sipariş Sayısı:</strong> ' . count($all_orders) . '</p>';

    // HPOS tablosunu doğrudan kontrol et
    global $wpdb;
    $hpos_orders_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}wc_orders");
    echo '<p><strong>HPOS Tablosundaki Sipariş Sayısı:</strong> ' . ($hpos_orders_count ?: 'Tablo bulunamadı') . '</p>';

    // Klasik posts tablosundaki siparişler
    $classic_orders_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'shop_order'");
    echo '<p><strong>Posts Tablosundaki Sipariş Sayısı:</strong> ' . $classic_orders_count . '</p>';
    
    // WooCommerce HPOS aktif mi kontrol et
    if (class_exists('Automattic\WooCommerce\Internal\DataStores\Orders\OrdersTableDataStore')) {
        echo '<p><strong>WooCommerce HPOS:</strong> Aktif</p>';
    } else {
        echo '<p><strong>WooCommerce HPOS:</strong> Pasif (Klasik post tablosu kullanılıyor)</p>';
    }
    
    echo '</div>';
}

// Admin panelinde debug bilgilerini göster
add_action('admin_notices', 'debug_superole_orders');

/**
 * Sipariş filtreleme hook'larının çalışıp çalışmadığını test et
 */
function test_order_filtering_hooks() {
    if (!is_admin()) {
        return;
    }
    
    $current_user = wp_get_current_user();
    if (!in_array('superole', $current_user->roles)) {
        return;
    }
    
    // Hook'ların eklenip eklenmediğini kontrol et
    global $wp_filter;
    
    echo '<div class="notice notice-warning">';
    echo '<h3>Hook Debug Bilgileri</h3>';
    
    $hooks_to_check = [
        'pre_get_posts',
        'posts_where',
        'woocommerce_orders_table_query_clauses',
        'woocommerce_order_list_table_prepare_items',
        'woocommerce_order_query_args',
        'wc_get_orders_args',
        'woocommerce_order_list_table_prepare_items_query_args'
    ];
    
    foreach ($hooks_to_check as $hook) {
        if (isset($wp_filter[$hook])) {
            echo '<p><strong>' . $hook . ':</strong> Aktif (' . count($wp_filter[$hook]->callbacks) . ' callback)</p>';
        } else {
            echo '<p><strong>' . $hook . ':</strong> Pasif</p>';
        }
    }
    
    echo '</div>';
}

add_action('admin_notices', 'test_order_filtering_hooks');
